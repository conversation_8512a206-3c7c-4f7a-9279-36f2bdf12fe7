<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TabZen</title>
    <link rel="stylesheet" href="styles.css">
</head>

<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="logo">
                    <div class="logo-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                            stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                            <!-- Zen circle -->
                            <circle cx="12" cy="12" r="8" stroke-dasharray="2,2"></circle>
                            <!-- Three stacked tabs -->
                            <rect x="5" y="6" width="14" height="3" rx="0.5" />
                            <rect x="6" y="8" width="12" height="3" rx="0.5" />
                            <rect x="7" y="10" width="10" height="3" rx="0.5" />
                            <!-- Zen dot -->
                            <circle cx="12" cy="12" r="1.5" fill="currentColor" />
                        </svg>
                    </div>
                    <h1>TabZen</h1>
                </div>
                <div class="tab-count" id="tabCount">0 tabs</div>
            </div>

            <!-- Search Bar -->
            <div class="search-container">
                <input type="text" id="searchTabs" class="search-input" placeholder="Search tabs..." />
            </div>

            <!-- Action Buttons -->
            <div class="actions">
                <button class="btn btn-primary" id="saveAllTabs">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                        stroke-linecap="round" stroke-linejoin="round">
                        <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                        <polyline points="17,21 17,13 7,13 7,21"></polyline>
                        <polyline points="7,3 7,8 15,8"></polyline>
                    </svg>
                    Save All Tabs
                </button>
                <button class="btn btn-secondary" id="restoreAllTabs" style="display: none;">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                        stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="23,4 23,10 17,10"></polyline>
                        <path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10"></path>
                    </svg>
                    Restore All
                </button>
            </div>
        </div>

        <!-- Saved Sessions -->
        <div class="content">
            <div class="empty-state" id="emptyState">
                <div class="empty-icon">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
                        stroke-linecap="round" stroke-linejoin="round">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                        <path d="m9 12 2 2 4-4"></path>
                    </svg>
                </div>
                <h3>No saved tabs yet</h3>
                <p>Click "Save All Tabs" to store your current browser session</p>
            </div>

            <div class="sessions-list" id="sessionsList">
                <!-- Saved sessions will be dynamically inserted here -->
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="stats" id="stats">
                <span id="sessionsCount">0 sessions</span>
                <span class="separator">•</span>
                <span id="totalTabsCount">0 total tabs</span>
            </div>
        </div>
    </div>

    <script src="popup.js"></script>
</body>

</html>