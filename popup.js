// TabZen Popup Script
class TabZen {
   constructor() {
      this.sessions = [];
   }

   static async initialize() {
      const app = new TabZen();
      await app.init();
      return app;
   }

   async init() {
      try {
         // Load sessions first
         await this.loadSessions();

         // Wait for DOM to be fully loaded
         if (document.readyState === "loading") {
            await new Promise((resolve) => {
               document.addEventListener("DOMContentLoaded", resolve);
            });
         }

         // Now bind events and update UI
         await this.bindEvents();
         this.updateUI();
         await this.updateCurrentTabCount();
      } catch (error) {
         console.error("Error initializing TabZen:", error);
         throw error; // Re-throw to be caught by the initializer
      }
   }

   async loadSessions() {
      try {
         const result = await chrome.storage.local.get(["tabzen_sessions"]);
         this.sessions = result.tabzen_sessions || [];
      } catch (error) {
         console.error("Error loading sessions:", error);
         this.sessions = [];
      }
   }

   async saveSessions() {
      try {
         await chrome.storage.local.set({ tabzen_sessions: this.sessions });
      } catch (error) {
         console.error("Error saving sessions:", error);
         throw error;
      }
   }

   async bindEvents() {
      try {
         // Static elements
         const elements = {
            saveAllTabs: document.getElementById("saveAllTabs"),
            restoreAllTabs: document.getElementById("restoreAllTabs"),
            searchTabs: document.getElementById("searchTabs"),
         };

         // Check if required elements exist
         if (!elements.saveAllTabs) {
            throw new Error("Required elements not found in the DOM");
         }

         // Bind static element events
         elements.saveAllTabs.addEventListener("click", () => this.saveAllTabs());

         if (elements.restoreAllTabs) {
            elements.restoreAllTabs.addEventListener("click", () => this.restoreAllTabs());
         }

         if (elements.searchTabs) {
            elements.searchTabs.addEventListener("input", (e) => this.searchTabs(e.target.value));
         }

         // Event delegation for dynamic elements
         document.addEventListener("click", async (e) => {
            const target = e.target.closest("[data-action]");
            if (!target) return;

            const action = target.dataset.action;
            const sessionId = target.dataset.sessionId;
            const tabIndex = target.dataset.tabIndex;

            switch (action) {
               case "restore-session":
                  await this.restoreSession(sessionId);
                  break;
               case "delete-session":
                  await this.deleteSession(sessionId);
                  break;
               case "restore-tab":
                  await this.restoreTab(sessionId, parseInt(tabIndex));
                  break;
               case "delete-tab":
                  await this.deleteTab(sessionId, parseInt(tabIndex));
                  break;
               case "toggle-tabs":
                  this.toggleTabsList(sessionId);
                  break;
               case "edit-session-name":
                  this.editSessionName(sessionId);
                  break;
               case "save-session-name":
                  this.saveSessionName(sessionId);
                  break;
               case "cancel-edit-session-name":
                  this.cancelEditSessionName(sessionId);
                  break;
            }
         });
      } catch (error) {
         console.error("Error binding events:", error);
         throw error;
      }
   }

   async updateCurrentTabCount() {
      try {
         const tabs = await chrome.tabs.query({ currentWindow: true });
         const tabCount = document.getElementById("tabCount");
         tabCount.textContent = `${tabs.length} tab${tabs.length !== 1 ? "s" : ""}`;
      } catch (error) {
         console.error("Error getting current tabs:", error);
      }
   }

   async saveAllTabs() {
      try {
         const tabs = await chrome.tabs.query({ currentWindow: true });
         if (tabs.length === 0) return;

         // Show session name input dialog
         this.showSessionNameDialog(tabs);
      } catch (error) {
         console.error("Error saving tabs:", error);
         this.showNotification("Error saving tabs", "error");
      }
   }

   showSessionNameDialog(tabs) {
      // Create overlay
      const overlay = document.createElement("div");
      overlay.className = "session-name-overlay";

      // Create dialog
      const dialog = document.createElement("div");
      dialog.className = "session-name-dialog";

      dialog.innerHTML = `
         <h3>Save Session</h3>
         <p>Enter a name for this session (${tabs.length} tabs):</p>
         <input type="text" class="session-name-dialog-input" placeholder="e.g., Work Research, Shopping, etc." maxlength="50">
         <div class="session-name-dialog-actions">
            <button class="btn btn-secondary cancel-session-save">Cancel</button>
            <button class="btn btn-primary save-session-confirm">Save Session</button>
         </div>
      `;

      overlay.appendChild(dialog);
      document.body.appendChild(overlay);

      const input = dialog.querySelector(".session-name-dialog-input");
      const cancelBtn = dialog.querySelector(".cancel-session-save");
      const saveBtn = dialog.querySelector(".save-session-confirm");

      // Set default name and select it
      input.value = this.generateSessionName();
      input.focus();
      input.select();

      // Handle save
      const handleSave = async () => {
         const sessionName = input.value.trim();
         if (!sessionName) {
            this.showNotification("Session name cannot be empty", "error");
            return;
         }

         await this.createSession(tabs, sessionName);
         overlay.remove();
      };

      // Handle cancel
      const handleCancel = () => {
         overlay.remove();
      };

      // Event listeners
      saveBtn.addEventListener("click", handleSave);
      cancelBtn.addEventListener("click", handleCancel);
      overlay.addEventListener("click", (e) => {
         if (e.target === overlay) handleCancel();
      });

      input.addEventListener("keydown", (e) => {
         if (e.key === "Enter") {
            handleSave();
         } else if (e.key === "Escape") {
            handleCancel();
         }
      });
   }

   async createSession(tabs, sessionName) {
      try {
         const session = {
            id: Date.now().toString(),
            name: sessionName,
            tabs: tabs.map((tab) => ({
               id: tab.id,
               title: tab.title,
               url: tab.url,
               favIconUrl: tab.favIconUrl,
            })),
            createdAt: new Date().toISOString(),
            tabCount: tabs.length,
         };

         this.sessions.unshift(session);
         await this.saveSessions();

         const tabsToClose = tabs.filter((tab) => !tab.pinned).map((tab) => tab.id);
         if (tabsToClose.length > 0) {
            await chrome.tabs.remove(tabsToClose);
         }

         this.updateUI();
         this.showNotification(`Saved "${sessionName}" with ${session.tabCount} tabs!`);
      } catch (error) {
         console.error("Error creating session:", error);
         this.showNotification("Error saving tabs", "error");
      }
   }

   async restoreSession(sessionId) {
      try {
         const session = this.sessions.find((s) => s.id === sessionId);
         if (!session) return;

         for (const tab of session.tabs) {
            await chrome.tabs.create({ url: tab.url, active: false });
         }

         this.showNotification(`Restored ${session.tabs.length} tabs!`);
      } catch (error) {
         console.error("Error restoring session:", error);
         this.showNotification("Error restoring tabs", "error");
      }
   }

   async restoreTab(sessionId, tabIndex) {
      try {
         const session = this.sessions.find((s) => s.id === sessionId);
         if (!session || !session.tabs[tabIndex]) return;

         const tab = session.tabs[tabIndex];
         await chrome.tabs.create({ url: tab.url, active: true });
         session.tabs.splice(tabIndex, 1);

         if (session.tabs.length === 0) {
            this.sessions = this.sessions.filter((s) => s.id !== sessionId);
         }

         await this.saveSessions();
         this.updateUI();
      } catch (error) {
         console.error("Error restoring tab:", error);
         this.showNotification("Error restoring tab", "error");
      }
   }

   async deleteSession(sessionId) {
      try {
         this.sessions = this.sessions.filter((s) => s.id !== sessionId);
         await this.saveSessions();
         this.updateUI();
         this.showNotification("Session deleted");
      } catch (error) {
         console.error("Error deleting session:", error);
      }
   }

   async deleteTab(sessionId, tabIndex) {
      try {
         const session = this.sessions.find((s) => s.id === sessionId);
         if (!session || !session.tabs[tabIndex]) return;

         session.tabs.splice(tabIndex, 1);
         if (session.tabs.length === 0) {
            this.sessions = this.sessions.filter((s) => s.id !== sessionId);
         }

         await this.saveSessions();
         this.updateUI();
      } catch (error) {
         console.error("Error deleting tab:", error);
      }
   }

   toggleTabsList(sessionId) {
      const tabsList = document.getElementById(`tabs-${sessionId}`);
      if (!tabsList) return;

      if (tabsList.classList.contains("collapsed")) {
         tabsList.classList.remove("collapsed");
         tabsList.classList.add("expanded");
      } else {
         tabsList.classList.remove("expanded");
         tabsList.classList.add("collapsed");
      }
   }

   editSessionName(sessionId) {
      const sessionNameElement = document.querySelector(`[data-session-id="${sessionId}"] .session-name`);
      const editButton = document.querySelector(`[data-session-id="${sessionId}"] .edit-session-btn`);

      if (!sessionNameElement || !editButton) return;

      const currentName = sessionNameElement.textContent;

      // Create input field
      const input = document.createElement("input");
      input.type = "text";
      input.value = currentName;
      input.className = "session-name-input";
      input.maxLength = 50;

      // Create save and cancel buttons
      const saveBtn = document.createElement("button");
      saveBtn.className = "btn-icon-small btn-save";
      saveBtn.innerHTML = `
         <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="20,6 9,17 4,12"></polyline>
         </svg>
      `;
      saveBtn.setAttribute("data-action", "save-session-name");
      saveBtn.setAttribute("data-session-id", sessionId);
      saveBtn.title = "Save name";

      const cancelBtn = document.createElement("button");
      cancelBtn.className = "btn-icon-small btn-cancel";
      cancelBtn.innerHTML = `
         <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
         </svg>
      `;
      cancelBtn.setAttribute("data-action", "cancel-edit-session-name");
      cancelBtn.setAttribute("data-session-id", sessionId);
      cancelBtn.title = "Cancel";

      // Replace session name with input and buttons
      sessionNameElement.style.display = "none";
      editButton.style.display = "none";

      const editContainer = document.createElement("div");
      editContainer.className = "session-name-edit-container";
      editContainer.appendChild(input);
      editContainer.appendChild(saveBtn);
      editContainer.appendChild(cancelBtn);

      sessionNameElement.parentNode.insertBefore(editContainer, sessionNameElement.nextSibling);

      // Focus input and select text
      input.focus();
      input.select();

      // Handle Enter key to save
      input.addEventListener("keydown", (e) => {
         if (e.key === "Enter") {
            this.saveSessionName(sessionId);
         } else if (e.key === "Escape") {
            this.cancelEditSessionName(sessionId);
         }
      });
   }

   saveSessionName(sessionId) {
      const editContainer = document.querySelector(`[data-session-id="${sessionId}"] .session-name-edit-container`);
      const input = editContainer?.querySelector(".session-name-input");

      if (!input) return;

      const newName = input.value.trim();
      if (!newName) {
         this.showNotification("Session name cannot be empty", "error");
         return;
      }

      // Update session in memory
      const session = this.sessions.find((s) => s.id === sessionId);
      if (session) {
         session.name = newName;
         this.saveSessions();
         this.updateUI();
         this.showNotification("Session name updated");
      }
   }

   cancelEditSessionName(sessionId) {
      const editContainer = document.querySelector(`[data-session-id="${sessionId}"] .session-name-edit-container`);
      const sessionNameElement = document.querySelector(`[data-session-id="${sessionId}"] .session-name`);
      const editButton = document.querySelector(`[data-session-id="${sessionId}"] .edit-session-btn`);

      if (editContainer) {
         editContainer.remove();
      }

      if (sessionNameElement) {
         sessionNameElement.style.display = "";
      }

      if (editButton) {
         editButton.style.display = "";
      }
   }

   updateUI() {
      const emptyState = document.getElementById("emptyState");
      const sessionsList = document.getElementById("sessionsList");
      const restoreAllBtn = document.getElementById("restoreAllTabs");

      if (this.sessions.length === 0) {
         emptyState.style.display = "flex";
         sessionsList.style.display = "none";
         restoreAllBtn.style.display = "none";
      } else {
         emptyState.style.display = "none";
         sessionsList.style.display = "block";
         restoreAllBtn.style.display = "inline-flex";
         this.renderSessions();
      }

      this.updateStats();
   }

   // Render sessions list
   renderSessions() {
      const sessionsList = document.getElementById("sessionsList");

      sessionsList.innerHTML = this.sessions
         .map(
            (session) => `
                <div class="session-card" data-session-id="${session.id}">
                    <div class="session-header">
                        <div class="session-info">
                            <div class="session-name-container">
                                <h4 class="session-name">${this.escapeHtml(session.name)}</h4>
                                <button class="btn-icon-small edit-session-btn" data-action="edit-session-name" data-session-id="${
                                   session.id
                                }" title="Edit session name">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                        <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                    </svg>
                                </button>
                            </div>
                            <div class="session-meta">
                                <span>${session.tabs.length} tab${session.tabs.length !== 1 ? "s" : ""}</span>
                                <span class="separator">•</span>
                                <span>${this.formatDate(session.createdAt)}</span>
                            </div>
                        </div>
                        <div class="session-actions">
                            <button class="btn-icon" data-action="restore-session" data-session-id="${session.id}" title="Restore all tabs">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="23,4 23,10 17,10"></polyline>
                                    <path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10"></path>
                                </svg>
                            </button>
                            <button class="btn-icon btn-danger" data-action="delete-session" data-session-id="${
                               session.id
                            }" title="Delete session">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="m3 6 18 0"></path>
                                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
               <div class="tabs-list ${session.tabs.length > 3 ? "collapsed" : ""}" id="tabs-${session.id}">
                    ${session.tabs
                       .map(
                          (tab, index) => `
                            <div class="tab-item">
                                <div class="tab-favicon">
                                    <img src="${
                                       tab.favIconUrl ||
                                       'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/></svg>'
                                    }"
                                         alt="" width="16" height="16" onerror="this.style.display='none'">
                                </div>
                                <div class="tab-content">
                                    <div class="tab-title">${this.escapeHtml(tab.title)}</div>
                                    <div class="tab-url">${this.escapeHtml(tab.url)}</div>
                                </div>
                                <div class="tab-actions">
                                    <button class="btn-icon-small" data-action="restore-tab" data-session-id="${
                                       session.id
                                    }" data-tab-index="${index}" title="Restore tab">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M21 9V7a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h7"></path>
                                            <path d="m16 16 5 5-5 5"></path>
                                        </svg>
                                    </button>
                                    <button class="btn-icon-small btn-danger" data-action="delete-tab" data-session-id="${
                                       session.id
                                    }" data-tab-index="${index}" title="Delete tab">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <line x1="18" y1="6" x2="6" y2="18"></line>
                                            <line x1="6" y1="6" x2="18" y2="18"></line>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        `,
                       )
                       .join("")}
                    ${
                       session.tabs.length > 3
                          ? `
                            <div class="tab-item more-tabs" data-action="toggle-tabs" data-session-id="${session.id}">
                                <div class="more-tabs-text">+ ${session.tabs.length - 3} more tabs</div>
                            </div>
                        `
                          : ""
                    }
                </div>
            </div>
        `,
         )
         .join("");
   }

   updateStats() {
      const sessionsCount = document.getElementById("sessionsCount");
      const totalTabsCount = document.getElementById("totalTabsCount");

      const totalTabs = this.sessions.reduce((sum, session) => sum + session.tabs.length, 0);
      sessionsCount.textContent = `${this.sessions.length} session${this.sessions.length !== 1 ? "s" : ""}`;
      totalTabsCount.textContent = `${totalTabs} total tab${totalTabs !== 1 ? "s" : ""}`;
   }

   escapeHtml(text) {
      const div = document.createElement("div");
      div.textContent = text;
      return div.innerHTML;
   }

   formatDate(dateString) {
      const date = new Date(dateString);
      const now = new Date();
      const diff = now.getTime() - date.getTime();
      const minutes = Math.floor(diff / (1000 * 60));
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const days = Math.floor(diff / (1000 * 60 * 60 * 24));
      if (minutes < 60) return `${minutes}m ago`;
      if (hours < 24) return `${hours}h ago`;
      return `${days}d ago`;
   }

   showNotification(message, type = "success") {
      const notification = document.createElement("div");
      notification.className = `notification notification-${type}`;
      notification.textContent = message;
      document.body.appendChild(notification);
      setTimeout(() => notification.remove(), 3000);
   }

   generateSessionName() {
      const now = new Date();
      return `Session ${now.toLocaleDateString()} ${now.toLocaleTimeString()}`;
   }

   // Search tabs by keyword
   searchTabs(keyword) {
      const lower = keyword.toLowerCase();
      const filtered = this.sessions.map((session) => {
         return {
            ...session,
            tabs: session.tabs.filter((tab) => tab.title.toLowerCase().includes(lower) || tab.url.toLowerCase().includes(lower)),
         };
      });
      this.renderSessions(filtered);
   }

   // Export sessions to JSON file
   exportSessions() {
      const blob = new Blob([JSON.stringify(this.sessions, null, 2)], { type: "application/json" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = "tabzen_sessions.json";
      link.click();
   }

   // Import sessions from JSON file
   async importSessions(file) {
      try {
         const text = await file.text();
         const imported = JSON.parse(text);
         if (!Array.isArray(imported)) throw new Error("Invalid format");
         this.sessions = [...imported, ...this.sessions];
         await this.saveSessions();
         this.updateUI();
         this.showNotification("Sessions imported!");
      } catch (err) {
         console.error("Import error:", err);
         this.showNotification("Failed to import", "error");
      }
   }

   // Restore all saved tabs
   async restoreAllTabs() {
      try {
         for (const session of this.sessions) {
            for (const tab of session.tabs) {
               await chrome.tabs.create({ url: tab.url, active: false });
            }
         }
         this.showNotification(`Restored all tabs from all sessions!`);
      } catch (error) {
         console.error("Error restoring all tabs:", error);
         this.showNotification("Error restoring all tabs", "error");
      }
   }
}

// Initialize TabZen when the popup loads
document.addEventListener("DOMContentLoaded", async () => {
   try {
      await TabZen.initialize();
   } catch (error) {
      console.error("Failed to initialize TabZen:", error);
      // Show error in the popup
      const errorDiv = document.createElement("div");
      errorDiv.className = "error-message";
      errorDiv.textContent = "Failed to initialize TabZen. Please try reloading.";
      document.body.prepend(errorDiv);
   }
});
